#!/usr/bin/env python3
"""
Отладка данных для таблицы статистики
"""

import sys
import os
import asyncio

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def debug_data():
    """Отладка получения данных"""
    
    try:
        from common.statistics import get_general_microtopics_detailed_data
        
        print("🔄 Получаем данные...")
        data = await get_general_microtopics_detailed_data()
        
        print(f"📊 Получено записей: {len(data)}")
        
        if data:
            print("\n📋 Первые 10 записей:")
            for i, item in enumerate(data[:10]):
                print(f"{i+1}. {item}")
                
            print(f"\n📋 Последние 5 записей:")
            for i, item in enumerate(data[-5:]):
                print(f"{len(data)-4+i}. {item}")
        else:
            print("❌ Данные пустые")
            
        return data
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_image_creation():
    """Тест создания изображения"""
    
    try:
        from common.statistics import create_statistics_table_image
        
        # Простые тестовые данные
        test_data = [
            {
                'name': 'JavaScript',
                'percentage': 0,
                'status': '',
                'is_header': True
            },
            {
                'name': 'Объекты',
                'percentage': 100.0,
                'status': '✅',
                'is_header': False
            },
            {
                'name': 'Массивы',
                'percentage': 0.0,
                'status': '❌',
                'is_header': False
            }
        ]
        
        print("\n🔄 Тестируем создание изображения...")
        image_bio = create_statistics_table_image(test_data, "Тест")
        
        print(f"✅ Изображение создано, размер: {len(image_bio.getvalue())} байт")
        
        # Сохраняем для проверки
        with open("debug_test.png", "wb") as f:
            f.write(image_bio.getvalue())
        print("📁 Сохранено как debug_test.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания изображения: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 Отладка данных и создания изображения")
    print("=" * 50)
    
    # Тест 1: Получение данных
    data = await debug_data()
    
    # Тест 2: Создание изображения
    image_success = await test_image_creation()
    
    print("\n" + "=" * 50)
    if data and image_success:
        print("🎉 Все тесты прошли успешно!")
    else:
        print("❌ Есть проблемы")

if __name__ == "__main__":
    asyncio.run(main())
