#!/usr/bin/env python3
"""
Финальный тест функции создания изображения таблицы
"""

import sys
import os
import asyncio

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_full_pipeline():
    """Тестируем полный пайплайн как в боте"""
    
    try:
        # Импортируем функции как в обработчике
        from common.statistics import get_general_microtopics_detailed_data, create_statistics_table_image
        
        print("✅ Импорты успешны")
        
        # Получаем данные
        print("🔄 Получаем данные...")
        data = await get_general_microtopics_detailed_data()
        print(f"✅ Получено {len(data)} записей")
        
        if not data:
            print("❌ Данные пустые")
            return False
        
        # Показываем структуру данных
        print("\n📋 Структура данных:")
        headers = [item for item in data if item.get('is_header', False)]
        regular = [item for item in data if not item.get('is_header', False)]
        
        print(f"   Заголовков предметов: {len(headers)}")
        print(f"   Строк с микротемами: {len(regular)}")
        
        # Проверяем статусы
        statuses = {}
        for item in regular:
            status = item.get('status', 'None')
            statuses[status] = statuses.get(status, 0) + 1
        
        print(f"   Статусы: {statuses}")
        
        # Создаем изображение
        print("\n🔄 Создаем изображение...")
        image_bio = create_statistics_table_image(
            data,
            "📊 Общая статистика по микротемам\n📈 Средний % понимания по всем предметам"
        )
        
        print(f"✅ Изображение создано, размер: {len(image_bio.getvalue())} байт")
        
        # Сохраняем
        with open("final_test_result.png", "wb") as f:
            f.write(image_bio.getvalue())
        
        print("📁 Результат сохранен как final_test_result.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 Финальный тест полного пайплайна")
    print("=" * 50)
    
    success = await test_full_pipeline()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Тест прошел успешно!")
        print("📋 Проверьте файл final_test_result.png")
        print("🔧 Если изображение правильное, проблема может быть в кэше бота")
    else:
        print("❌ Тест не прошел")

if __name__ == "__main__":
    asyncio.run(main())
