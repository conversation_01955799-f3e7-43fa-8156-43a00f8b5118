"""
Миграция для создания таблицы unregistered_users и обновления course_entry_test_results
Создаем таблицу для незарегистрированных пользователей и обновляем связи
"""
import asyncio
import sys
import os
from sqlalchemy import text

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database.database import get_db_session


async def migrate_course_entry_test_results():
    """Миграция для создания unregistered_users и обновления course_entry_test_results"""
    print("🔄 Начинаем миграцию для незарегистрированных пользователей...")

    async with get_db_session() as session:
        try:
            # 1. Проверяем, существует ли уже таблица unregistered_users
            check_table = await session.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_name = 'unregistered_users'
            """))
            table_exists = check_table.fetchone() is not None

            if table_exists:
                print("   ✅ Таблица unregistered_users уже существует")
            else:
                # 2. Создаем таблицу unregistered_users
                print("   📋 Создаем таблицу unregistered_users...")
                await session.execute(text("""
                    CREATE TABLE unregistered_users (
                        id SERIAL PRIMARY KEY,
                        telegram_id BIGINT UNIQUE NOT NULL,
                        username VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                print("   ✅ Таблица unregistered_users создана")

            # 3. Проверяем структуру course_entry_test_results
            check_columns = await session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'course_entry_test_results'
                AND column_name = 'unregistered_user_id'
            """))
            has_new_column = check_columns.fetchone() is not None

            if has_new_column:
                print("   ✅ Таблица course_entry_test_results уже обновлена")
                return
            
            # 4. Проверяем существующие данные
            print("   📋 Проверяем существующие данные...")
            existing_data = await session.execute(text("""
                SELECT COUNT(*) FROM course_entry_test_results
            """))
            count = existing_data.scalar()
            print(f"   📊 Найдено {count} существующих записей")

            if count > 0:
                print("   ⚠️ ВНИМАНИЕ: Существующие данные будут удалены")
                print("   ⚠️ Входной тест курса теперь работает только с незарегистрированными пользователями")
                # Удаляем все существующие записи
                await session.execute(text("DELETE FROM course_entry_test_results"))
                print("   🗑️ Существующие данные удалены")

            # 5. Удаляем старые ограничения и внешние ключи
            print("   🗑️ Удаляем старые ограничения...")
            try:
                await session.execute(text("""
                    ALTER TABLE course_entry_test_results
                    DROP CONSTRAINT IF EXISTS unique_course_entry_test_per_student_subject
                """))
                await session.execute(text("""
                    ALTER TABLE course_entry_test_results
                    DROP CONSTRAINT IF EXISTS course_entry_test_results_student_id_fkey
                """))
            except Exception as e:
                print(f"   ⚠️ Предупреждение при удалении ограничений: {e}")

            # 6. Удаляем старую колонку student_id и добавляем новую
            print("   🔄 Обновляем структуру таблицы...")
            await session.execute(text("""
                ALTER TABLE course_entry_test_results
                DROP COLUMN IF EXISTS student_id
            """))
            await session.execute(text("""
                ALTER TABLE course_entry_test_results
                ADD COLUMN unregistered_user_id INTEGER NOT NULL REFERENCES unregistered_users(id) ON DELETE CASCADE
            """))

            # 7. Добавляем новое ограничение уникальности
            print("   🔐 Добавляем новое ограничение уникальности...")
            await session.execute(text("""
                ALTER TABLE course_entry_test_results
                ADD CONSTRAINT unique_course_entry_test_per_unregistered_user_subject
                UNIQUE (unregistered_user_id, subject_id)
            """))
            
            await session.commit()
            print("   ✅ Миграция успешно завершена!")
            
        except Exception as e:
            await session.rollback()
            print(f"   ❌ Ошибка при миграции: {e}")
            raise


if __name__ == "__main__":
    asyncio.run(migrate_course_entry_test_results())
