"""
Репозиторий для работы с незарегистрированными пользователями
"""
from typing import Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from ..models import UnregisteredUser
from ..database import get_db_session


class UnregisteredUserRepository:
    """Репозиторий для работы с незарегистрированными пользователями"""
    
    @staticmethod
    async def get_by_telegram_id(telegram_id: int) -> Optional[UnregisteredUser]:
        """Получить незарегистрированного пользователя по telegram_id"""
        async with get_db_session() as session:
            result = await session.execute(
                select(UnregisteredUser)
                .where(UnregisteredUser.telegram_id == telegram_id)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def create_or_update(telegram_id: int, username: str = None) -> UnregisteredUser:
        """Создать или обновить незарегистрированного пользователя"""
        async with get_db_session() as session:
            # Проверяем, существует ли уже пользователь
            existing_user = await session.execute(
                select(UnregisteredUser)
                .where(UnregisteredUser.telegram_id == telegram_id)
            )
            user = existing_user.scalar_one_or_none()
            
            if user:
                # Обновляем username если он изменился
                if user.username != username:
                    user.username = username
                    await session.commit()
                    await session.refresh(user)
            else:
                # Создаем нового пользователя
                user = UnregisteredUser(
                    telegram_id=telegram_id,
                    username=username
                )
                session.add(user)
                await session.commit()
                await session.refresh(user)
            
            return user
    
    @staticmethod
    async def get_by_id(user_id: int) -> Optional[UnregisteredUser]:
        """Получить незарегистрированного пользователя по ID"""
        async with get_db_session() as session:
            result = await session.execute(
                select(UnregisteredUser)
                .where(UnregisteredUser.id == user_id)
            )
            return result.scalar_one_or_none()
