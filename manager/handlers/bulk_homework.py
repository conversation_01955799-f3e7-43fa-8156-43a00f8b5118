import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from common.keyboards import get_universal_back_button
from database import MicrotopicRepository, SubjectRepository

# Настройка логирования
logger = logging.getLogger(__name__)

router = Router()

class BulkHomeworkStates(StatesGroup):
    input_template = State()           # Ввод текстового шаблона
    waiting_for_photo = State()        # Ожидание загрузки фото для конкретного вопроса


def parse_homework_template(text: str) -> dict:
    """
    Парсинг шаблона ДЗ
    
    Ожидаемый формат:
    Название ДЗ: Базовое
    
    Вопрос: Какова общая формула алканов?
    Вариант: CnH2n+2
    Вариант: CnH2n
    Вариант: CnH2n-2
    Микротема: 1
    Правильный ответ на вопрос: CnH2n+2
    Время ответа: 30
    Возможность добавить фото к вопросу: +
    
    Возвращает: {
        'test_name': str,
        'questions': [
            {
                'text': str,
                'options': {'A': str, 'B': str, ...},  # Автоматически A,B,C...
                'correct_answer': str,  # Буква правильного ответа
                'microtopic_number': int,
                'time_limit': int,
                'needs_photo': bool
            }
        ],
        'errors': [str]  # Список ошибок валидации
    }
    """
    errors = []
    questions = []
    test_name = ""
    
    try:
        # 1. Извлекаем название ДЗ
        if "Название ДЗ:" not in text:
            errors.append("Не найдено поле 'Название ДЗ:'")
        else:
            name_line = [line for line in text.split('\n') if line.strip().startswith('Название ДЗ:')]
            if name_line:
                test_name = name_line[0].split('Название ДЗ:', 1)[1].strip()
                if not test_name:
                    errors.append("Название ДЗ не может быть пустым")
            else:
                errors.append("Не удалось извлечь название ДЗ")
        
        # 2. Разделяем на блоки вопросов
        question_blocks = []
        lines = text.split('\n')
        current_block = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('Вопрос:'):
                if current_block:  # Сохраняем предыдущий блок
                    question_blocks.append('\n'.join(current_block))
                current_block = [line]  # Начинаем новый блок
            elif current_block:  # Добавляем к текущему блоку
                current_block.append(line)
        
        if current_block:  # Добавляем последний блок
            question_blocks.append('\n'.join(current_block))
        
        if not question_blocks:
            errors.append("Не найдено ни одного вопроса")
            return {'test_name': test_name, 'questions': questions, 'errors': errors}
        
        # 3. Обрабатываем каждый блок вопроса
        for i, block in enumerate(question_blocks, 1):
            question_errors, question_data = parse_question_block(block, i)
            errors.extend(question_errors)
            if question_data:
                questions.append(question_data)
    
    except Exception as e:
        errors.append(f"Ошибка парсинга шаблона: {str(e)}")
    
    return {
        'test_name': test_name,
        'questions': questions, 
        'errors': errors
    }


def parse_question_block(block: str, question_num: int) -> tuple:
    """Парсинг одного блока вопроса"""
    errors = []
    question_data = None
    
    try:
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        
        # Извлекаем данные
        question_text = ""
        variants = []
        microtopic_number = None
        correct_answer_text = ""
        time_limit = None
        needs_photo = False
        
        for line in lines:
            if line.startswith('Вопрос:'):
                question_text = line.split('Вопрос:', 1)[1].strip()
            elif line.startswith('Вариант:'):
                variant_text = line.split('Вариант:', 1)[1].strip()
                if variant_text:
                    variants.append(variant_text)
            elif line.startswith('Микротема:'):
                try:
                    microtopic_number = int(line.split('Микротема:', 1)[1].strip())
                    if microtopic_number < 1:
                        errors.append(f"Вопрос {question_num}: Номер микротемы должен быть больше 0")
                except ValueError:
                    errors.append(f"Вопрос {question_num}: Номер микротемы должен быть числом")
            elif line.startswith('Правильный ответ на вопрос:'):
                correct_answer_text = line.split('Правильный ответ на вопрос:', 1)[1].strip()
            elif line.startswith('Время ответа:'):
                try:
                    time_limit = int(line.split('Время ответа:', 1)[1].strip())
                    if not (10 <= time_limit <= 600):
                        errors.append(f"Вопрос {question_num}: Время ответа должно быть от 10 до 600 секунд")
                except ValueError:
                    errors.append(f"Вопрос {question_num}: Время ответа должно быть числом")
            elif line.startswith('Возможность добавить фото к вопросу:'):
                photo_flag = line.split('Возможность добавить фото к вопросу:', 1)[1].strip()
                needs_photo = photo_flag == '+'
        
        # Валидация обязательных полей
        if not question_text:
            errors.append(f"Вопрос {question_num}: Текст вопроса не может быть пустым")
        
        if len(variants) < 2:
            errors.append(f"Вопрос {question_num}: Необходимо минимум 2 варианта ответа")
        
        if microtopic_number is None:
            errors.append(f"Вопрос {question_num}: Не указан номер микротемы")
        
        if not correct_answer_text:
            errors.append(f"Вопрос {question_num}: Не указан правильный ответ")
        
        if time_limit is None:
            errors.append(f"Вопрос {question_num}: Не указано время ответа")
        
        # Формируем варианты ответов с автоматическими буквами A, B, C...
        options = {}
        letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
        
        for i, variant in enumerate(variants[:10]):  # Максимум 10 вариантов
            if i < len(letters):
                options[letters[i]] = variant
        
        # Находим букву правильного ответа
        correct_answer_letter = None
        for letter, text in options.items():
            if text == correct_answer_text:
                correct_answer_letter = letter
                break
        
        if not correct_answer_letter:
            errors.append(f"Вопрос {question_num}: Правильный ответ '{correct_answer_text}' не найден среди вариантов")
        
        # Если нет ошибок - формируем данные вопроса
        if not errors:
            question_data = {
                'text': question_text,
                'options': options,
                'correct_answer': correct_answer_letter,
                'microtopic_number': microtopic_number,
                'time_limit': time_limit,
                'needs_photo': needs_photo
            }
    
    except Exception as e:
        errors.append(f"Вопрос {question_num}: Ошибка обработки - {str(e)}")
    
    return errors, question_data


@router.callback_query(F.data == "bulk_creation")
async def start_bulk_creation(callback: CallbackQuery, state: FSMContext):
    """Начало создания ДЗ из шаблона"""
    template_example = """Название ДЗ: Базовое

Вопрос: Какова общая формула алканов?
Вариант: CnH2n+2
Вариант: CnH2n
Вариант: CnH2n-2
Вариант: CnHn
Микротема: 1
Правильный ответ на вопрос: CnH2n+2
Время ответа: 30
Возможность добавить фото к вопросу: +

Вопрос: Что такое изомерия?
Вариант: Одинаковые молекулы
Вариант: Разные свойства при одинаковой формуле
Вариант: Разные формулы
Микротема: 2
Правильный ответ на вопрос: Разные свойства при одинаковой формуле
Время ответа: 45
Возможность добавить фото к вопросу: -"""
    
    await callback.message.edit_text(
        f"📝 Создание ДЗ из текстового шаблона\n\n"
        f"Скопируйте и заполните шаблон:\n\n"
        f"```\n{template_example}\n```\n\n"
        f"Отправьте заполненный шаблон:",
        reply_markup=get_universal_back_button(),
        parse_mode="Markdown"
    )
    await state.set_state(BulkHomeworkStates.input_template)


@router.message(BulkHomeworkStates.input_template)
async def process_template(message: Message, state: FSMContext):
    """Обработка текстового шаблона"""
    parsed_data = parse_homework_template(message.text)
    
    if parsed_data['errors']:
        # Показываем ВСЕ ошибки и просим исправить ВЕСЬ шаблон
        errors_text = "\n".join([f"❌ {error}" for error in parsed_data['errors']])
        await message.answer(
            f"🚫 Найдены ошибки в шаблоне:\n\n{errors_text}\n\n"
            f"❗ Пожалуйста, исправьте ВСЕ ошибки в шаблоне и отправьте заново.\n"
            f"Обработка будет продолжена только после устранения всех ошибок.",
            reply_markup=get_universal_back_button()
        )
        return
    
    # Сохраняем данные и начинаем обработку
    await state.update_data(
        test_name=parsed_data['test_name'],
        parsed_questions=parsed_data['questions'],
        current_question_index=0,
        processed_questions=[]
    )
    
    await message.answer(
        f"✅ Шаблон успешно обработан!\n"
        f"📝 Название ДЗ: {parsed_data['test_name']}\n"
        f"❓ Количество вопросов: {len(parsed_data['questions'])}\n\n"
        f"Начинаем обработку вопросов..."
    )
    
    await process_next_question(message, state)


async def process_next_question(message: Message, state: FSMContext):
    """Обработка следующего вопроса из шаблона"""
    user_data = await state.get_data()
    parsed_questions = user_data.get('parsed_questions', [])
    current_index = user_data.get('current_question_index', 0)

    if current_index >= len(parsed_questions):
        # Все вопросы обработаны - переходим к подтверждению
        await finalize_bulk_homework(message, state)
        return

    current_question = parsed_questions[current_index]

    if current_question['needs_photo']:
        # Нужно фото - останавливаемся и просим загрузить
        await message.answer(
            f"📸 Прикрепите фото к вопросу:\n\n"
            f"**{current_question['text']}**",
            parse_mode="Markdown"
        )
        await state.set_state(BulkHomeworkStates.waiting_for_photo)
    else:
        # Фото не нужно - обрабатываем вопрос и переходим к следующему
        await process_question_without_photo(message, state, current_question)


@router.message(BulkHomeworkStates.waiting_for_photo)
async def process_bulk_photo(message: Message, state: FSMContext):
    """Обработка фото в bulk-режиме"""
    if not message.photo:
        user_data = await state.get_data()
        parsed_questions = user_data.get('parsed_questions', [])
        current_index = user_data.get('current_question_index', 0)
        current_question = parsed_questions[current_index]

        await message.answer(
            f"❌ Пожалуйста, отправьте фото для вопроса:\n\n"
            f"**{current_question['text']}**",
            parse_mode="Markdown"
        )
        return

    # Сохраняем фото к текущему вопросу
    photo_id = message.photo[-1].file_id
    user_data = await state.get_data()
    current_index = user_data.get('current_question_index', 0)
    parsed_questions = user_data.get('parsed_questions', [])

    current_question = parsed_questions[current_index]
    current_question['photo_id'] = photo_id

    await state.update_data(parsed_questions=parsed_questions)

    await message.answer("✅ Фото добавлено! Продолжаем обработку...")

    # Обрабатываем текущий вопрос и переходим к следующему
    await process_question_with_photo(message, state, current_question)


async def process_question_without_photo(message: Message, state: FSMContext, question_data: dict):
    """Обработка вопроса без фото"""
    await validate_microtopic_and_process(message, state, question_data)


async def process_question_with_photo(message: Message, state: FSMContext, question_data: dict):
    """Обработка вопроса с фото"""
    await validate_microtopic_and_process(message, state, question_data)


async def validate_microtopic_and_process(message: Message, state: FSMContext, question_data: dict):
    """Валидация микротемы и обработка вопроса"""
    user_data = await state.get_data()
    subject_id = user_data.get('subject_id')

    # ПЕРЕИСПОЛЬЗУЕМ: Проверяем микротему (логика из process_topic)
    try:
        # Получаем предмет
        subject = await SubjectRepository.get_by_id(subject_id)
        if not subject:
            await handle_critical_error(message, state, "Предмет не найден")
            return

        # Ищем микротему по номеру
        microtopic = await MicrotopicRepository.get_by_number(subject_id, question_data['microtopic_number'])

        if not microtopic:
            # ПЕРЕИСПОЛЬЗУЕМ: Логику показа доступных микротем из process_topic
            await handle_missing_microtopic(message, state, question_data, subject)
            return

        # Микротема найдена - формируем финальный вопрос
        # ПЕРЕИСПОЛЬЗУЕМ: Формат данных как в существующих обработчиках
        final_question = {
            'text': question_data['text'],
            'options': question_data['options'],  # Уже в формате {'A': 'text', 'B': 'text'}
            'correct_answer': question_data['correct_answer'],  # Уже буква A, B, C
            'microtopic_id': microtopic.id,
            'microtopic_name': microtopic.name,
            'topic_number': question_data['microtopic_number'],
            'time_limit': question_data['time_limit'],
            'photo_id': question_data.get('photo_id')
        }

        # Добавляем в обработанные вопросы
        processed_questions = user_data.get('processed_questions', [])
        processed_questions.append(final_question)

        # Переходим к следующему вопросу
        current_index = user_data.get('current_question_index', 0) + 1
        await state.update_data(
            processed_questions=processed_questions,
            current_question_index=current_index
        )

        await process_next_question(message, state)

    except Exception as e:
        await handle_critical_error(message, state, f"Ошибка обработки вопроса: {str(e)}")


async def handle_missing_microtopic(message: Message, state: FSMContext, question_data: dict, subject):
    """Обработка отсутствующей микротемы"""
    # ПЕРЕИСПОЛЬЗУЕМ: Логику из process_topic() для показа доступных микротем
    try:
        microtopics = await MicrotopicRepository.get_by_subject(subject.id)

        # Показываем доступные микротемы (как в process_topic)
        available_topics = ""
        if microtopics:
            available_topics = "\n📋 Доступные микротемы:\n"
            for mt in microtopics[:10]:  # Показываем первые 10
                available_topics += f"   {mt.number}. {mt.name}\n"

            if len(microtopics) > 10:
                available_topics += f"   ... и еще {len(microtopics) - 10} микротем\n"

        await message.answer(
            f"❌ Микротема с номером {question_data['microtopic_number']} не найдена "
            f"в предмете '{subject.name}'.\n"
            f"{available_topics}\n"
            f"🔧 Пожалуйста, исправьте номер микротемы в шаблоне и отправьте заново.\n\n"
            f"❗ Обработка остановлена. Необходимо исправить шаблон.",
            reply_markup=get_universal_back_button()
        )

        # Возвращаемся к вводу шаблона
        await state.set_state(BulkHomeworkStates.input_template)

    except Exception as e:
        await handle_critical_error(message, state, f"Ошибка получения микротем: {str(e)}")


async def handle_critical_error(message: Message, state: FSMContext, error_text: str):
    """Обработка критических ошибок"""
    logger.error(f"Critical error in bulk homework: {error_text}")

    await message.answer(
        f"🚫 Критическая ошибка: {error_text}\n\n"
        f"Пожалуйста, начните создание ДЗ заново.",
        reply_markup=get_universal_back_button()
    )

    # Очищаем состояние и возвращаемся к главному меню
    await state.clear()


async def finalize_bulk_homework(message: Message, state: FSMContext):
    """Завершение создания ДЗ из шаблона"""
    user_data = await state.get_data()
    processed_questions = user_data.get('processed_questions', [])

    if not processed_questions:
        await message.answer(
            "❌ Не удалось обработать ни одного вопроса. Проверьте шаблон.",
            reply_markup=get_universal_back_button()
        )
        return

    # ПЕРЕИСПОЛЬЗУЕМ: Преобразуем данные в формат для confirm_test()
    await state.update_data(questions=processed_questions)

    # ПЕРЕИСПОЛЬЗУЕМ: confirm_test() для показа предварительного просмотра
    from common.manager_tests.handlers import confirm_test

    # Создаем совместимый callback объект
    class FakeCallback:
        def __init__(self, message):
            self.message = message
            self.data = "finish_adding_questions"

        async def answer(self):
            pass

    fake_callback = FakeCallback(message)

    # Устанавливаем правильное состояние для confirm_test
    from manager.handlers.homework import AddHomeworkStates
    await state.set_state(AddHomeworkStates.confirm_test)

    await confirm_test(fake_callback, state)
