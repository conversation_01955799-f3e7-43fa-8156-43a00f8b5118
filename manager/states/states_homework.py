from common.manager_tests.get_transitions_handlers import get_transitions_handlers
from manager.handlers.homework import (
    start_add_homework, select_subject, select_lesson, save_homework,
    select_homework_to_delete, show_homeworks_to_delete, show_homework_management,
    start_delete_homework
)
from manager.handlers.homework import AddHomeworkStates
transitions, handlers = get_transitions_handlers(AddHomeworkStates, "manager")
# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    AddHomeworkStates.main: None,  # Возврат в главное меню менеджера
    # Поток добавления ДЗ
    AddHomeworkStates.select_course: AddHomeworkStates.main,  # Возврат к выбору действия (добавить/удалить)
    AddHomeworkStates.select_subject: AddHomeworkStates.select_course,
    AddHomeworkStates.select_lesson: AddHomeworkStates.select_subject,
    AddHomeworkStates.choose_creation_method: AddHomeworkStates.select_lesson,  # Возврат к выбору урока
    # Поток удаления ДЗ
    AddHomeworkStates.delete_test: AddHomeworkStates.main,  # Возврат к выбору действия
    AddHomeworkStates.select_test_to_delete: AddHomeworkStates.delete_test,
    **transitions
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AddHomeworkStates.main: show_homework_management,  # Показ меню управления ДЗ
    # Поток добавления ДЗ
    AddHomeworkStates.select_course: start_add_homework,
    AddHomeworkStates.select_subject: select_subject,
    AddHomeworkStates.select_lesson: select_lesson,
    # Поток удаления ДЗ
    AddHomeworkStates.delete_test: start_delete_homework,
    AddHomeworkStates.select_test_to_delete: show_homeworks_to_delete,
    **handlers
}