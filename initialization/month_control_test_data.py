"""
Создание тестовых данных для контрольных тестов месяца
"""
from database import (
    MonthControlTestResultRepository, StudentRepository, MonthTestRepository, 
    QuestionRepository, HomeworkRepository, LessonRepository
)


async def create_month_control_test_results():
    """Создание тестовых результатов контрольных тестов месяца"""
    try:
        print("📊 Создание тестовых результатов контрольных тестов месяца...")
        
        # Проверяем, есть ли уже результаты
        existing_results = await MonthControlTestResultRepository.get_all()
        if existing_results:
            print("   ⚠️ Результаты контрольных тестов месяца уже существуют, пропускаем создание")
            return
        
        # Получаем студентов и тесты месяца
        students = await StudentRepository.get_all()
        month_tests = await MonthTestRepository.get_all()
        
        if not students or not month_tests:
            print("   ⚠️ Студенты или тесты месяца не найдены, пропускаем создание результатов")
            return

        # Создаем тестовые результаты контрольных тестов (с ростом по сравнению с входными)
        test_data = [
            # Группа М-1 (Математика) - Андрей Климов (админ)
            {
                "student_name": "Андрей Климов",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 96,  # Рост с 92% до 96% = +4.3%
            },
            {
                "student_name": "Андрей Климов",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 94,  # Рост с 88% до 94% = +6.8%
            },
            
            # Аружан Ахметова (группа М-1)
            {
                "student_name": "Аружан Ахметова",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 89,  # Рост с 85% до 89% = +4.7%
            },
            {
                "student_name": "Аружан Ахметова",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 78,  # Рост с 72% до 78% = +8.3%
            },

            # Группа М-2 (Математика)
            {
                "student_name": "Муханбетжан Олжас",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 85,  # Рост с 78% до 85% = +9.0%
            },
            {
                "student_name": "Муханбетжан Олжас",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 72,  # Рост с 65% до 72% = +10.8%
            },
            {
                "student_name": "Ерасыл Мухамедов",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 62,  # Рост с 55% до 62% = +12.7%
            },
            {
                "student_name": "Ерасыл Мухамедов",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 48,  # Рост с 42% до 48% = +14.3%
            },

            # Группа PY-1 (Python)
            {
                "student_name": "Муханбетжан Олжас",
                "month_test_name": "Основы программирования",
                "correct_percentage": 93,  # Рост с 90% до 93% = +3.3%
            },
            {
                "student_name": "Андрей Климов",
                "month_test_name": "Основы программирования",
                "correct_percentage": 98,  # Рост с 95% до 98% = +3.2%
            },
            
            # Дополнительные студенты группы М-1 (с ростом)
            {
                "student_name": "Алия Сейтова",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 82,  # Рост с 76% до 82% = +7.9%
            },
            {
                "student_name": "Алия Сейтова",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 75,  # Рост с 68% до 75% = +10.3%
            },
            {
                "student_name": "Данияр Жумабеков",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 93,  # Рост с 89% до 93% = +4.5%
            },
            {
                "student_name": "Данияр Жумабеков",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 88,  # Рост с 82% до 88% = +7.3%
            },
            {
                "student_name": "Айгерим Касымова",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 78,  # Рост с 71% до 78% = +9.9%
            },
            {
                "student_name": "Айгерим Касымова",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 71,  # Рост с 64% до 71% = +10.9%
            },

            # Дополнительные студенты группы М-2 (с ростом)
            {
                "student_name": "Арман Жумабеков",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 89,  # Рост с 83% до 89% = +7.2%
            },
            {
                "student_name": "Арман Жумабеков",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 84,  # Рост с 77% до 84% = +9.1%
            },
            {
                "student_name": "Сабина Ахметова",
                "month_test_name": "Контрольный тест по алгебре",
                "correct_percentage": 76,  # Рост с 69% до 76% = +10.1%
            },
            {
                "student_name": "Сабина Ахметова",
                "month_test_name": "Геометрия и фигуры",
                "correct_percentage": 65,  # Рост с 58% до 65% = +12.1%
            },

            # Дополнительные студенты группы PY-1 (с ростом)
            {
                "student_name": "Ерлан Токтаров",
                "month_test_name": "Основы программирования",
                "correct_percentage": 91,  # Рост с 87% до 91% = +4.6%
            },
            {
                "student_name": "Айгерим Сериккызы",
                "month_test_name": "Основы программирования",
                "correct_percentage": 85,  # Рост с 79% до 85% = +7.6%
            },

            # Дополнительные студенты для IT курса (высокий рост)
            {
                "student_name": "Бекзат Сериков",  # PY-2
                "month_test_name": "Основы программирования",
                "correct_percentage": 89,  # Рост с 65% до 89% = +36.9%
            },
            {
                "student_name": "Динара Жанибекова",  # JS-1
                "month_test_name": "Основы программирования",
                "correct_percentage": 84,  # Рост с 58% до 84% = +44.8%
            },
            {
                "student_name": "Нурлан Касымов",  # Б-1
                "month_test_name": "Основы программирования",
                "correct_percentage": 92,  # Рост с 74% до 92% = +24.3%
            },

            # Дополнительные студенты для ЕНТ курса (умеренный рост)
            {
                "student_name": "Максат Ибрагимов",  # Ф-1
                "month_test_name": "Механика и движение",
                "correct_percentage": 78,  # Рост с 72% до 78% = +8.3%
            },
            {
                "student_name": "Асель Токтарова",  # Х-1
                "month_test_name": "Химические реакции",
                "correct_percentage": 73,  # Рост с 68% до 73% = +7.4%
            }
        ]

        created_count = 0
        
        for data in test_data:
            # Находим студента
            student = None
            for s in students:
                if s.user.name == data["student_name"]:
                    student = s
                    break
            
            if not student:
                print(f"   ⚠️ Студент '{data['student_name']}' не найден")
                continue

            # Находим тест месяца
            month_test = None
            for mt in month_tests:
                if mt.name == data["month_test_name"]:
                    month_test = mt
                    break
            
            if not month_test:
                print(f"   ⚠️ Тест месяца '{data['month_test_name']}' не найден")
                continue

            # Проверяем, не проходил ли уже студент этот контрольный тест
            existing_result = await MonthControlTestResultRepository.get_by_student_and_month_test(
                student.id, month_test.id
            )
            if existing_result:
                print(f"   ⚠️ {student.user.name} уже проходил контрольный тест '{month_test.name}'")
                continue

            # Создаем демонстрационные результаты (без реальных вопросов)
            total_questions = 9  # 3 микротемы * 3 вопроса
            target_correct = int(total_questions * data["correct_percentage"] / 100)

            question_results = []
            for i in range(total_questions):
                # Создаем демонстрационные результаты
                microtopic_number = (i // 3) + 1  # Микротемы 1, 2, 3
                is_correct = i < target_correct

                question_results.append({
                    'question_id': 1,  # Демонстрационный ID вопроса
                    'selected_answer_id': 1,  # Демонстрационный ID ответа
                    'is_correct': is_correct,
                    'time_spent': 25,  # Примерное время ответа
                    'microtopic_number': microtopic_number
                })

            # Создаем результат контрольного теста
            test_result = await MonthControlTestResultRepository.create_test_result(
                student_id=student.id,
                month_test_id=month_test.id,
                question_results=question_results
            )

            print(f"   ✅ Результат контрольного теста месяца создан: {student.user.name} - {month_test.name} ({test_result.score_percentage}%)")
            created_count += 1

        print(f"📊 Создание тестовых результатов контрольных тестов месяца завершено! Создано: {created_count}")

    except Exception as e:
        print(f"❌ Ошибка при создании тестовых результатов контрольных тестов месяца: {e}")
        import traceback
        traceback.print_exc()
