"""
Создание тестовых данных для входных тестов курса
"""
from database import (
    CourseEntryTestResultRepository, SubjectRepository
)


async def create_course_entry_test_results():
    """Создание тестовых результатов входных тестов курса для незарегистрированных пользователей"""
    try:
        print("📊 Создание тестовых результатов входных тестов курса для незарегистрированных пользователей...")

        # Проверяем, есть ли уже результаты
        existing_results = await CourseEntryTestResultRepository.get_all()
        if existing_results:
            print("   ⚠️ Результаты входных тестов курса уже существуют, пропускаем создание")
            return

        # Получаем предметы
        subjects = await SubjectRepository.get_all()

        if not subjects:
            print("   ⚠️ Предметы не найдены, пропускаем создание результатов")
            return

        # Создаем тестовые результаты для незарегистрированных пользователей
        test_cases = [
            {
                "telegram_id": 111111111,
                "username": "test_user_1",
                "subject_name": "Математика",
                "correct_percentage": 80,  # 80% правильных ответов
            },
            {
                "telegram_id": 222222222,
                "username": "test_user_2",
                "subject_name": "Математика",
                "correct_percentage": 60,  # 60% правильных ответов
            },
            {
                "telegram_id": 333333333,
                "username": "test_user_3",
                "subject_name": "Python",
                "correct_percentage": 93,  # 93% правильных ответов
            },
            {
                "telegram_id": 333333333,
                "username": "test_user_3",
                "subject_name": "Математика",
                "correct_percentage": 75,  # 75% правильных ответов
            },
            {
                "telegram_id": 444444444,
                "username": "test_user_4",
                "subject_name": "Python",
                "correct_percentage": 85,  # 85% правильных ответов
            },
            {
                "telegram_id": 555555555,
                "username": "test_user_5",
                "subject_name": "Физика",
                "correct_percentage": 70,  # 70% правильных ответов
            },
            {
                "telegram_id": 666666666,
                "username": "test_user_6",
                "subject_name": "Химия",
                "correct_percentage": 65,  # 65% правильных ответов
            }
        ]

        created_count = 0

        for test_case in test_cases:
            telegram_id = test_case["telegram_id"]
            username = test_case["username"]

            # Находим предмет
            subject = None
            for subj in subjects:
                if subj.name == test_case["subject_name"]:
                    subject = subj
                    break

            if not subject:
                print(f"   ⚠️ Предмет '{test_case['subject_name']}' не найден")
                continue

            # Проверяем, есть ли уже результат для этого пользователя и предмета
            existing_result = await CourseEntryTestResultRepository.get_by_telegram_id_and_subject(
                telegram_id, subject.id
            )

            if existing_result:
                print(f"   ⚠️ Результат для пользователя {username} по {subject.name} уже существует")
                continue

            # Получаем вопросы для предмета (до 30 штук)
            questions = await CourseEntryTestResultRepository.get_random_questions_for_subject(
                subject.id, 30
            )

            if len(questions) == 0:
                print(f"   ⚠️ Нет вопросов для предмета '{subject.name}'")
                continue

            total_questions = len(questions)
            target_correct = int((test_case["correct_percentage"] / 100) * total_questions)

            # Создаем результаты ответов
            question_results = []

            for i, question in enumerate(questions):
                # Первые target_correct вопросов - правильные, остальные - неправильные
                is_correct = i < target_correct

                # Выбираем ответ
                correct_answer = None
                wrong_answer = None

                for answer in question.answer_options:
                    if answer.is_correct:
                        correct_answer = answer
                    else:
                        wrong_answer = answer

                selected_answer = correct_answer if is_correct else wrong_answer

                question_results.append({
                    'question_id': question.id,
                    'selected_answer_id': selected_answer.id if selected_answer else None,
                    'is_correct': is_correct,
                    'time_spent': 20 + i,  # Разное время ответа
                    'microtopic_number': question.microtopic_number
                })

            # Создаем результат теста для незарегистрированного пользователя
            try:
                test_result = await CourseEntryTestResultRepository.create_test_result(
                    telegram_id=telegram_id,
                    username=username,
                    subject_id=subject.id,
                    question_results=question_results
                )

                print(f"   ✅ Результат входного теста создан для незарегистрированного пользователя {username} по предмету {subject.name} ({test_result.score_percentage}%, {test_result.correct_answers}/{test_result.total_questions})")
                created_count += 1

            except Exception as e:
                print(f"   ❌ Ошибка при создании результата для пользователя {username}: {e}")

        print(f"📊 Создание тестовых результатов входных тестов курса для незарегистрированных пользователей завершено! Создано: {created_count}")

    except Exception as e:
        print(f"❌ Ошибка при создании тестовых результатов входных тестов курса: {e}")
