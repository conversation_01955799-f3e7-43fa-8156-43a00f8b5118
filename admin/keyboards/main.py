from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_universal_back_button, back_to_main_button, get_main_menu_back_button
from admin.utils.common import get_confirmation_kb, get_tariff_selection_kb


def get_admin_main_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню админа"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📖 Предметы", callback_data="admin_subjects")],
        [InlineKeyboardButton(text="📚 Курсы", callback_data="admin_courses")],
        [InlineKeyboardButton(text="👥 Группы", callback_data="admin_groups")],
        [InlineKeyboardButton(text="🎓 Ученики", callback_data="admin_students")],
        [InlineKeyboardButton(text="👨‍🏫 Кураторы", callback_data="admin_curators")],
        [InlineKeyboardButton(text="👩‍🏫 Преподаватели", callback_data="admin_teachers")],
        [InlineKeyboardButton(text="👨‍💼 Менеджеры", callback_data="admin_managers")]
    ])

def get_admin_entity_menu_kb(entity_name: str, entity_name_accusative: str, callback_prefix: str) -> InlineKeyboardMarkup:
    """Универсальная клавиатура меню управления сущностями

    Args:
        entity_name: Название сущности в именительном падеже (курс, предмет, группа, ученик, куратор, преподаватель, менеджер)
        entity_name_accusative: Название сущности в винительном падеже (курс, предмет, группу, ученика, куратора, преподавателя, менеджера)
        callback_prefix: Префикс для callback_data (course, subject, group, student, curator, teacher, manager)
    """
    buttons = [
        [InlineKeyboardButton(text=f"➕ Добавить {entity_name_accusative}", callback_data=f"add_{callback_prefix}")],
    ]

    # Для предметов, курсов, групп, учеников, кураторов, преподавателей и менеджеров добавляем кнопку списка вместо удаления
    if callback_prefix == "subject":
        buttons.append([InlineKeyboardButton(text="📋 Список предметов", callback_data="list_subjects")])
    elif callback_prefix == "course":
        buttons.append([InlineKeyboardButton(text="📋 Список курсов", callback_data="list_courses")])
    elif callback_prefix == "group":
        buttons.append([InlineKeyboardButton(text="📋 Список групп", callback_data="list_groups")])
    elif callback_prefix == "student":
        buttons.append([InlineKeyboardButton(text="📋 Список учеников", callback_data="list_students")])
    elif callback_prefix == "curator":
        buttons.append([InlineKeyboardButton(text="📋 Список кураторов", callback_data="list_curators")])
    elif callback_prefix == "teacher":
        buttons.append([InlineKeyboardButton(text="📋 Список преподавателей", callback_data="list_teachers")])
    elif callback_prefix == "manager":
        buttons.append([InlineKeyboardButton(text="📋 Список менеджеров", callback_data="list_managers")])
    else:
        # Для остальных сущностей оставляем кнопку удаления
        buttons.append([InlineKeyboardButton(text=f"🗑 Убрать {entity_name_accusative}", callback_data=f"remove_{callback_prefix}")])

    buttons.extend(get_main_menu_back_button())
    return InlineKeyboardMarkup(inline_keyboard=buttons)

def get_home_kb() -> InlineKeyboardMarkup:
    """Кнопка возврата в главное меню"""
    return InlineKeyboardMarkup(inline_keyboard=[back_to_main_button()])

def get_home_and_back_kb() -> InlineKeyboardMarkup:
    """Кнопки возврата назад и в главное меню"""
    return InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button())

# Функции get_tariff_selection_kb и get_confirmation_kb теперь импортируются из admin.utils.common
