from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from admin.utils.common import (
    add_group, remove_group, update_group,
    get_subjects_list_kb, get_groups_list_kb, get_confirmation_kb,
    get_subject_by_id, get_group_by_id, get_group_by_name, get_groups_list
)
from admin.keyboards.main import get_home_kb, get_home_and_back_kb

router = Router()

class AdminGroupsStates(StatesGroup):
    # Состояния для добавления группы
    enter_group_name = State()
    select_group_subject = State()
    confirm_add_group = State()

    # Состояния для удаления группы (используется только при редактировании)
    confirm_delete_group = State()

    # Состояния для просмотра и редактирования групп
    groups_list = State()
    edit_group = State()
    enter_new_group_name = State()
    select_new_group_subject = State()
    confirm_edit_group = State()

# === ДОБАВЛЕНИЕ ГРУППЫ ===

@router.callback_query(F.data == "add_group")
async def start_add_group(callback: CallbackQuery, state: FSMContext):
    """Начать добавление группы"""
    await callback.message.edit_text(
        text="Введите название группы:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminGroupsStates.enter_group_name)

@router.message(StateFilter(AdminGroupsStates.enter_group_name))
async def process_group_name(message: Message, state: FSMContext):
    """Обработать ввод названия группы"""
    group_name = message.text.strip()

    await state.update_data(group_name=group_name)
    await state.set_state(AdminGroupsStates.select_group_subject)

    await message.answer(
        text=f"Группа: {group_name}\n\nВыберите предмет, к которому относится группа:",
        reply_markup=await get_subjects_list_kb("group_subject")
    )

@router.callback_query(AdminGroupsStates.select_group_subject, F.data.startswith("group_subject_"))
async def select_subject_for_group(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для группы"""
    subject_id = int(callback.data.replace("group_subject_", ""))
    data = await state.get_data()
    group_name = data.get("group_name", "")

    # Получаем название предмета для отображения
    subject = await get_subject_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже группа с таким названием для данного предмета
    try:
        existing_group = await get_group_by_name(group_name, subject_id)
        if existing_group:
            await callback.message.edit_text(
                text=f"❌ Группа '{group_name}' уже существует для предмета '{subject.name}'!\n\n"
                     f"Введите другое название группы:",
                reply_markup=get_home_kb()
            )
            await state.set_state(AdminGroupsStates.enter_group_name)
            return
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при проверке существования группы!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(group_subject_id=subject_id, group_subject_name=subject.name)
    await state.set_state(AdminGroupsStates.confirm_add_group)

    await callback.message.edit_text(
        text=f"📋 Подтверждение создания группы:\n\n"
             f"Название: {group_name}\n"
             f"Предмет: {subject.name}",
        reply_markup=get_confirmation_kb("add", "group")
    )

@router.callback_query(StateFilter(AdminGroupsStates.confirm_add_group), F.data.startswith("confirm_add_group_"))
async def confirm_add_group(callback: CallbackQuery, state: FSMContext):
    """Подтвердить добавление группы"""
    data = await state.get_data()
    group_name = data.get("group_name", "")
    subject_id = data.get("group_subject_id")
    subject_name = data.get("group_subject_name", "")

    success = await add_group(group_name, subject_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Группа '{group_name}' успешно создана для предмета '{subject_name}'!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Группа '{group_name}' уже существует для предмета '{subject_name}'!",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ПРОСМОТР И РЕДАКТИРОВАНИЕ ГРУПП ===

@router.callback_query(F.data == "list_groups")
async def show_groups_list(callback: CallbackQuery, state: FSMContext):
    """Показать список групп для редактирования"""
    try:
        # Получаем список групп
        groups = await get_groups_list()

        if not groups:
            await callback.message.edit_text(
                text="📋 Список групп пуст!\n\n"
                     "Сначала добавьте группы для управления ими.",
                reply_markup=get_home_kb()
            )
            return

        # Создаем клавиатуру со списком групп для редактирования
        groups_kb = await get_groups_list_kb("edit_group")
        await callback.message.edit_text(
            text="👥 Список групп\n\n"
                 "Выберите группу для редактирования:",
            reply_markup=groups_kb
        )
        await state.set_state(AdminGroupsStates.groups_list)

    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке списка групп!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminGroupsStates.groups_list), F.data.startswith("edit_group_"))
async def start_edit_group(callback: CallbackQuery, state: FSMContext):
    """Начать редактирование группы"""
    try:
        group_id = int(callback.data.replace("edit_group_", ""))
        group = await get_group_by_id(group_id)

        if not group:
            await callback.message.edit_text(
                text="❌ Группа не найдена!",
                reply_markup=get_home_kb()
            )
            return

        await state.update_data(
            group_to_edit=group_id,
            current_group_name=group.name,
            current_subject_id=group.subject_id,
            current_subject_name=group.subject.name if group.subject else "Неизвестный предмет"
        )
        await state.set_state(AdminGroupsStates.edit_group)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить название", callback_data="change_group_name")],
            [InlineKeyboardButton(text="📖 Изменить предмет", callback_data="change_group_subject")],
            [InlineKeyboardButton(text="🗑 Удалить группу", callback_data=f"delete_group_{group_id}")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_groups_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"👥 Редактирование группы\n\n"
                 f"📝 Название: {group.name}\n"
                 f"📖 Предмет: {group.subject.name if group.subject else 'Неизвестный предмет'}\n"
                 f"🆔 ID: {group.id}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID группы!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных группы!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminGroupsStates.edit_group), F.data == "change_group_name")
async def start_change_group_name(callback: CallbackQuery, state: FSMContext):
    """Начать изменение названия группы"""
    data = await state.get_data()
    current_name = data.get("current_group_name", "")
    current_subject_name = data.get("current_subject_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение названия группы\n\n"
             f"Предмет: {current_subject_name}\n"
             f"Текущее название: {current_name}\n\n"
             f"Введите новое название группы:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminGroupsStates.enter_new_group_name)

@router.message(StateFilter(AdminGroupsStates.enter_new_group_name))
async def process_new_group_name(message: Message, state: FSMContext):
    """Обработать ввод нового названия группы"""
    new_group_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_group_name", "")
    current_subject_id = data.get("current_subject_id")
    current_subject_name = data.get("current_subject_name", "")
    group_id = data.get("group_to_edit")

    # Проверяем, не совпадает ли новое название с текущим
    if new_group_name == current_name:
        await message.answer(
            text=f"⚠️ Новое название совпадает с текущим!\n\n"
                 f"Введите другое название группы:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже группа с таким названием для данного предмета
    try:
        existing_group = await get_group_by_name(new_group_name, current_subject_id)
        if existing_group:
            await message.answer(
                text=f"❌ Группа с названием '{new_group_name}' уже существует для предмета '{current_subject_name}'!\n\n"
                     f"Введите другое название группы:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования группы!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_group_name=new_group_name)
    await state.set_state(AdminGroupsStates.confirm_edit_group)

    await message.answer(
        text=f"📋 Подтверждение изменения группы:\n\n"
             f"Предмет: {current_subject_name}\n"
             f"Текущее название: {current_name}\n"
             f"Новое название: {new_group_name}",
        reply_markup=get_confirmation_kb("edit", "group", str(group_id))
    )

@router.callback_query(StateFilter(AdminGroupsStates.edit_group), F.data == "change_group_subject")
async def start_change_group_subject(callback: CallbackQuery, state: FSMContext):
    """Начать изменение предмета группы"""
    data = await state.get_data()
    current_name = data.get("current_group_name", "")
    current_subject_name = data.get("current_subject_name", "")

    await callback.message.edit_text(
        text=f"📖 Изменение предмета группы\n\n"
             f"Группа: {current_name}\n"
             f"Текущий предмет: {current_subject_name}\n\n"
             f"Выберите новый предмет:",
        reply_markup=await get_subjects_list_kb("new_group_subject")
    )
    await state.set_state(AdminGroupsStates.select_new_group_subject)

@router.callback_query(StateFilter(AdminGroupsStates.select_new_group_subject), F.data.startswith("new_group_subject_"))
async def select_new_group_subject(callback: CallbackQuery, state: FSMContext):
    """Выбрать новый предмет для группы"""
    new_subject_id = int(callback.data.replace("new_group_subject_", ""))
    new_subject = await get_subject_by_id(new_subject_id)

    if not new_subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_home_kb()
        )
        return

    data = await state.get_data()
    current_name = data.get("current_group_name", "")
    current_subject_id = data.get("current_subject_id")
    current_subject_name = data.get("current_subject_name", "")
    group_id = data.get("group_to_edit")

    # Проверяем, не совпадает ли новый предмет с текущим
    if new_subject_id == current_subject_id:
        await callback.message.edit_text(
            text=f"⚠️ Новый предмет совпадает с текущим!\n\n"
                 f"Выберите другой предмет:",
            reply_markup=await get_subjects_list_kb("new_group_subject")
        )
        return

    # Проверяем, существует ли уже группа с таким названием для нового предмета
    try:
        existing_group = await get_group_by_name(current_name, new_subject_id)
        if existing_group:
            await callback.message.edit_text(
                text=f"❌ Группа с названием '{current_name}' уже существует для предмета '{new_subject.name}'!\n\n"
                     f"Выберите другой предмет:",
                reply_markup=await get_subjects_list_kb("new_group_subject")
            )
            return
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при проверке существования группы!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_subject_id=new_subject_id, new_subject_name=new_subject.name)
    await state.set_state(AdminGroupsStates.confirm_edit_group)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения группы:\n\n"
             f"Название: {current_name}\n"
             f"Текущий предмет: {current_subject_name}\n"
             f"Новый предмет: {new_subject.name}",
        reply_markup=get_confirmation_kb("edit", "group", str(group_id))
    )

@router.callback_query(StateFilter(AdminGroupsStates.confirm_edit_group), F.data.startswith("confirm_edit_group_"))
async def confirm_edit_group(callback: CallbackQuery, state: FSMContext):
    """Подтвердить редактирование группы"""
    data = await state.get_data()
    group_id = data.get("group_to_edit")
    new_name = data.get("new_group_name")
    new_subject_id = data.get("new_subject_id")
    current_name = data.get("current_group_name", "")
    current_subject_name = data.get("current_subject_name", "")
    new_subject_name = data.get("new_subject_name", "")

    try:
        # Определяем, что именно обновляем
        if new_name and new_subject_id is not None:
            # Обновляем и название, и предмет
            success = await update_group(group_id, new_name, new_subject_id)
            success_text = f"✅ Группа успешно обновлена!\n\n" \
                          f"Название: {current_name} → {new_name}\n" \
                          f"Предмет: {current_subject_name} → {new_subject_name}"
        elif new_name:
            # Обновляем только название
            success = await update_group(group_id, new_name)
            success_text = f"✅ Название группы успешно обновлено!\n\n" \
                          f"Предмет: {current_subject_name}\n" \
                          f"Старое название: {current_name}\n" \
                          f"Новое название: {new_name}"
        elif new_subject_id is not None:
            # Обновляем только предмет
            success = await update_group(group_id, subject_id=new_subject_id)
            success_text = f"✅ Предмет группы '{current_name}' успешно обновлен!\n\n" \
                          f"Старый предмет: {current_subject_name}\n" \
                          f"Новый предмет: {new_subject_name}"
        else:
            # Ничего не обновляем
            await callback.message.edit_text(
                text="⚠️ Нет данных для обновления!",
                reply_markup=get_home_kb()
            )
            await state.clear()
            return

        if success:
            await callback.message.edit_text(
                text=success_text,
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении группы!\n"
                     f"Возможно, группа с таким названием уже существует для выбранного предмета.",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении группы!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()

@router.callback_query(StateFilter(AdminGroupsStates.edit_group), F.data == "back_to_groups_list")
async def back_to_groups_list(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку групп"""
    # Вызываем функцию показа списка групп
    await show_groups_list(callback, state)

# === УДАЛЕНИЕ ГРУППЫ (только через редактирование) ===

@router.callback_query(StateFilter(AdminGroupsStates.edit_group), F.data.startswith("delete_group_"))
async def select_group_to_delete(callback: CallbackQuery, state: FSMContext):
    """Выбрать группу для удаления из режима редактирования"""
    try:
        group_id = int(callback.data.replace("delete_group_", ""))
        group = await get_group_by_id(group_id)

        if not group:
            await callback.message.edit_text(
                text="❌ Группа не найдена!",
                reply_markup=get_home_kb()
            )
            return

        await state.update_data(
            group_to_delete=group_id,
            group_name=group.name,
            subject_name=group.subject.name if group.subject else "Неизвестный предмет"
        )
        await state.set_state(AdminGroupsStates.confirm_delete_group)

        await callback.message.edit_text(
            text=f"🗑 Подтверждение удаления группы:\n\n"
                 f"Название: {group.name}\n"
                 f"Предмет: {group.subject.name if group.subject else 'Неизвестный предмет'}\n\n"
                 f"⚠️ Это действие нельзя отменить!",
            reply_markup=get_confirmation_kb("delete", "group", str(group_id))
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID группы!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных группы!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminGroupsStates.confirm_delete_group), F.data.startswith("confirm_delete_group_"))
async def confirm_delete_group(callback: CallbackQuery, state: FSMContext):
    """Подтвердить удаление группы"""
    data = await state.get_data()
    group_id = data.get("group_to_delete")
    group_name = data.get("group_name", "")
    subject_name = data.get("subject_name", "")

    success = await remove_group(group_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Группа '{group_name}' успешно удалена из предмета '{subject_name}'!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Группа '{group_name}' не найдена!",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ОБРАБОТЧИКИ ОТМЕНЫ РЕДАКТИРОВАНИЯ ===

@router.callback_query(StateFilter(AdminGroupsStates.confirm_edit_group), F.data.startswith("cancel_edit_group"))
async def cancel_edit_group(callback: CallbackQuery, state: FSMContext):
    """Отменить редактирование группы"""
    await callback.message.edit_text(
        text="❌ Редактирование группы отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ОБРАБОТЧИКИ ОТМЕНЫ ===

@router.callback_query(StateFilter(AdminGroupsStates.confirm_add_group), F.data.startswith("cancel_add_group"))
async def cancel_add_group(callback: CallbackQuery, state: FSMContext):
    """Отменить добавление группы"""
    await callback.message.edit_text(
        text="❌ Добавление группы отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

@router.callback_query(StateFilter(AdminGroupsStates.confirm_delete_group), F.data.startswith("cancel_delete_group"))
async def cancel_delete_group(callback: CallbackQuery, state: FSMContext):
    """Отменить удаление группы"""
    await callback.message.edit_text(
        text="❌ Удаление группы отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ОТМЕНА ДЕЙСТВИЙ ===

@router.callback_query(F.data.startswith("cancel_add_group") | F.data.startswith("cancel_delete_group"))
async def cancel_group_action(callback: CallbackQuery, state: FSMContext):
    """Отменить действие с группой"""
    await callback.message.edit_text(
        text="❌ Действие отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
