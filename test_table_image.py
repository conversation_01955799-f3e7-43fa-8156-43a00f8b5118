#!/usr/bin/env python3
"""
Тест для функции создания изображения таблицы статистики
"""

import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.statistics import create_statistics_table_image

def test_create_table_image():
    """Тестируем создание изображения таблицы"""
    
    # Тестовые данные
    test_data = [
        {
            'name': '📚 Химия',
            'percentage': 0,
            'status': '',
            'is_header': True
        },
        {
            'name': 'Простые и сложные вещества. Атом и молекула',
            'percentage': 67.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Расположение электронов в атомах',
            'percentage': 83.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Элементы s, p, d, f. Электронная конфигурация',
            'percentage': 25.0,
            'status': '❌',
            'is_header': False
        },
        {
            'name': 'Изотопы',
            'percentage': 0.0,
            'status': '❌',
            'is_header': False
        },
        {
            'name': 'Закономерности изменения свойств элементов в периодической таблице',
            'percentage': 75.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Энергетические уровни. Квантовые числа',
            'percentage': 73.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Ядерные реакции',
            'percentage': 100.0,
            'status': '✅',
            'is_header': False
        }
    ]
    
    try:
        print("🔄 Создаем изображение таблицы...")
        
        # Создаем изображение
        image_bio = create_statistics_table_image(
            test_data, 
            "📊 Общая статистика по микротемам\n📈 Средний % понимания по всем предметам"
        )
        
        # Сохраняем для проверки
        with open("test_statistics_table.png", "wb") as f:
            f.write(image_bio.getvalue())
        
        print("✅ Изображение создано успешно!")
        print("📁 Файл сохранен как: test_statistics_table.png")
        print(f"📊 Размер файла: {len(image_bio.getvalue())} байт")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании изображения: {e}")
        return False

def test_empty_data():
    """Тестируем создание изображения с пустыми данными"""
    
    try:
        print("\n🔄 Тестируем пустые данные...")
        
        # Создаем изображение с пустыми данными
        image_bio = create_statistics_table_image([], "Тест пустых данных")
        
        # Сохраняем для проверки
        with open("test_empty_table.png", "wb") as f:
            f.write(image_bio.getvalue())
        
        print("✅ Изображение с пустыми данными создано успешно!")
        print("📁 Файл сохранен как: test_empty_table.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании изображения с пустыми данными: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование функции создания изображения таблицы")
    print("=" * 60)
    
    # Тест 1: Обычные данные
    success1 = test_create_table_image()
    
    # Тест 2: Пустые данные
    success2 = test_empty_data()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 Все тесты прошли успешно!")
    else:
        print("❌ Некоторые тесты не прошли")
