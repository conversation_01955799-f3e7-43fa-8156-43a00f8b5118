#!/usr/bin/env python3
"""
Тест исправленной функции создания изображения таблицы статистики
"""

import sys
import os

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.statistics import create_statistics_table_image

def test_fixed_table():
    """Тестируем исправленную функцию"""
    
    # Тестовые данные как в реальной системе
    test_data = [
        {
            'name': 'JavaScript',
            'percentage': 0,
            'status': '',
            'is_header': True
        },
        {
            'name': 'Объекты',
            'percentage': 100.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Массивы',
            'percentage': 0.0,
            'status': '❌',
            'is_header': False
        },
        {
            'name': 'DOM',
            'percentage': 100.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Python',
            'percentage': 0,
            'status': '',
            'is_header': True
        },
        {
            'name': 'Переменные',
            'percentage': 100.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Типы данных',
            'percentage': 82.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Условия',
            'percentage': 100.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Циклы',
            'percentage': 50.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Функции',
            'percentage': 64.5,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Классы',
            'percentage': 75.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Модули',
            'percentage': 90.0,
            'status': '✅',
            'is_header': False
        },
        {
            'name': 'Исключения',
            'percentage': 78.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Файлы',
            'percentage': 78.0,
            'status': '⚠️',
            'is_header': False
        },
        {
            'name': 'Библиотеки',
            'percentage': 20.0,
            'status': '❌',
            'is_header': False
        }
    ]
    
    try:
        print("🔄 Создаем исправленное изображение таблицы...")
        
        # Создаем изображение
        image_bio = create_statistics_table_image(
            test_data, 
            "📊 Общая статистика по микротемам\n📈 Средний % понимания по всем предметам"
        )
        
        # Сохраняем для проверки
        with open("fixed_statistics_table.png", "wb") as f:
            f.write(image_bio.getvalue())
        
        print("✅ Исправленное изображение создано успешно!")
        print("📁 Файл сохранен как: fixed_statistics_table.png")
        print(f"📊 Размер файла: {len(image_bio.getvalue())} байт")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании изображения: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Тестирование исправленной функции создания изображения таблицы")
    print("=" * 70)
    
    success = test_fixed_table()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Тест прошел успешно!")
        print("📋 Проверьте файл fixed_statistics_table.png")
    else:
        print("❌ Тест не прошел")
